/* Modern Light Tabulator Theme with High Contrast - Theme Aware */

/* Main container with modern font */
.tabulator {
    border: 1px solid #d0d0d0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    font-size: 14px;
    overflow: hidden;
    position: relative;
    text-align: left;
    background-color: #ffffff;
    color: #1a1a1a;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transform: translateZ(0);
}

/* Theme-aware background adjustments for light themes */
[data-theme="light"] .tabulator,
[data-theme="cupcake"] .tabulator,
[data-theme="bumblebee"] .tabulator,
[data-theme="emerald"] .tabulator,
[data-theme="corporate"] .tabulator,
[data-theme="retro"] .tabulator,
[data-theme="cyberpunk"] .tabulator,
[data-theme="valentine"] .tabulator,
[data-theme="garden"] .tabulator,
[data-theme="lofi"] .tabulator,
[data-theme="pastel"] .tabulator,
[data-theme="fantasy"] .tabulator,
[data-theme="wireframe"] .tabulator,
[data-theme="cmyk"] .tabulator,
[data-theme="autumn"] .tabulator,
[data-theme="acid"] .tabulator,
[data-theme="lemonade"] .tabulator,
[data-theme="winter"] .tabulator,
[data-theme="nord"] .tabulator,
[data-theme="caramellatte"] .tabulator,
[data-theme="silk"] .tabulator {
    background-color: #f8f9fa;
    border-color: #e9ecef;
}

/* Theme-aware header styling for light themes */
[data-theme="light"] .tabulator .tabulator-header,
[data-theme="cupcake"] .tabulator .tabulator-header,
[data-theme="bumblebee"] .tabulator .tabulator-header,
[data-theme="emerald"] .tabulator .tabulator-header,
[data-theme="corporate"] .tabulator .tabulator-header,
[data-theme="retro"] .tabulator .tabulator-header,
[data-theme="cyberpunk"] .tabulator .tabulator-header,
[data-theme="valentine"] .tabulator .tabulator-header,
[data-theme="garden"] .tabulator .tabulator-header,
[data-theme="lofi"] .tabulator .tabulator-header,
[data-theme="pastel"] .tabulator .tabulator-header,
[data-theme="fantasy"] .tabulator .tabulator-header,
[data-theme="wireframe"] .tabulator .tabulator-header,
[data-theme="cmyk"] .tabulator .tabulator-header,
[data-theme="autumn"] .tabulator .tabulator-header,
[data-theme="acid"] .tabulator .tabulator-header,
[data-theme="lemonade"] .tabulator .tabulator-header,
[data-theme="winter"] .tabulator .tabulator-header,
[data-theme="nord"] .tabulator .tabulator-header,
[data-theme="caramellatte"] .tabulator .tabulator-header,
[data-theme="silk"] .tabulator .tabulator-header {
    background: linear-gradient(135deg, #e28743 0%, #d67a3a 100%);
    color: #000000;
    border-bottom: 2px solid #d67a3a;
}

.tabulator[tabulator-layout=fitDataFill] .tabulator-tableholder .tabulator-table {
    min-width: 100%;
}

.tabulator[tabulator-layout=fitDataTable] {
    display: inline-block;
}

.tabulator.tabulator-block-select,
.tabulator.tabulator-ranges .tabulator-cell:not(.tabulator-editing) {
    user-select: none;
}

/* Header styling with clean light design - fallback for non-theme-aware */
.tabulator .tabulator-header {
    background: linear-gradient(135deg, #e28743 0%, #d67a3a 100%);
    border-bottom: 2px solid #d67a3a;
    box-sizing: border-box;
    color: #000000;
    font-weight: 600;
    outline: none;
    overflow: hidden;
    position: relative;
    user-select: none;
    white-space: nowrap;
    width: 100%;
}

.tabulator .tabulator-header.tabulator-header-hidden {
    display: none;
}

.tabulator .tabulator-header .tabulator-header-contents {
    overflow: hidden;
    position: relative;
}

.tabulator .tabulator-header .tabulator-header-contents .tabulator-headers {
    display: inline-block;
}

/* Column headers with improved styling */
.tabulator .tabulator-header .tabulator-col {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-right: 1px solid #dee2e6;
    box-sizing: border-box;
    display: inline-flex;
    flex-direction: column;
    justify-content: flex-start;
    overflow: hidden;
    position: relative;
    text-align: left;
    vertical-align: bottom;
    transition: background-color 0.2s ease;
}

.tabulator .tabulator-header .tabulator-col:hover {
    background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
}

.tabulator .tabulator-header .tabulator-col.tabulator-moving {
    background: #f1f3f4;
    border: 1px solid #adb5bd;
    pointer-events: none;
    position: absolute;
}

.tabulator .tabulator-header .tabulator-col.tabulator-range-highlight {
    background-color: #e3f2fd;
    color: #1565c0;
}

.tabulator .tabulator-header .tabulator-col.tabulator-range-selected {
    background-color: #1976d2;
    color: #ffffff;
}

.tabulator .tabulator-header .tabulator-col .tabulator-col-content {
    box-sizing: border-box;
    padding: 8px 12px;
    position: relative;
}

.tabulator .tabulator-header .tabulator-col .tabulator-col-content .tabulator-header-popup-button {
    padding: 0 8px;
}

.tabulator .tabulator-header .tabulator-col .tabulator-col-content .tabulator-header-popup-button:hover {
    cursor: pointer;
    opacity: 0.7;
}

.tabulator .tabulator-header .tabulator-col .tabulator-col-content .tabulator-col-title-holder {
    position: relative;
}

.tabulator .tabulator-header .tabulator-col .tabulator-col-content .tabulator-col-title {
    box-sizing: border-box;
    overflow: hidden;
    text-overflow: ellipsis;
    vertical-align: bottom;
    white-space: nowrap;
    width: 100%;
    font-weight: 600;
    letter-spacing: 0.3px;
}

.tabulator .tabulator-header .tabulator-col .tabulator-col-content .tabulator-col-title.tabulator-col-title-wrap {
    text-overflow: clip;
    white-space: normal;
}

/* Table body with clean white background */
.tabulator .tabulator-tableholder {
    overflow: auto;
    position: relative;
    white-space: nowrap;
    width: 100%;
    background-color: #ffffff;
}

.tabulator .tabulator-tableholder:focus {
    outline: none;
}

.tabulator .tabulator-tableholder .tabulator-table {
    background-color: #ffffff;
    color: #1a1a1a;
    display: inline-block;
    overflow: visible;
    position: relative;
    white-space: nowrap;
}

/* Row styling with high contrast */
.tabulator-row {
    background-color: #ffffff;
    box-sizing: border-box;
    min-height: 32px;
    position: relative;
    transition: background-color 0.15s ease;
    border-bottom: 1px solid #e9ecef;
}

.tabulator-row.tabulator-row-even {
    background-color: #f8f9fa;
}

.tabulator-row.tabulator-selectable:hover {
    background-color: #e3f2fd;
    cursor: pointer;
}

.tabulator-row.tabulator-selected {
    background-color: #1976d2;
    color: #ffffff;
}

.tabulator-row.tabulator-selected:hover {
    background-color: #1565c0;
    cursor: pointer;
}

/* Cell styling with better padding and contrast */
.tabulator-row .tabulator-cell {
    border-right: 1px solid #e9ecef;
    box-sizing: border-box;
    display: inline-block;
    outline: none;
    overflow: hidden;
    padding: 10px 12px;
    position: relative;
    text-overflow: ellipsis;
    vertical-align: middle;
    white-space: nowrap;
    font-weight: 400;
    line-height: 1.4;
    color: #1a1a1a;
}

.tabulator-row .tabulator-cell.tabulator-row-header {
    background: #e9ecef;
    border-bottom: 1px solid #dee2e6;
    border-right: 1px solid #dee2e6;
    font-weight: 600;
}

/* Footer styling */
.tabulator .tabulator-footer {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-top: 2px solid #dee2e6;
    color: #212529;
    font-weight: 500;
    user-select: none;
    white-space: nowrap;
}

.tabulator .tabulator-footer .tabulator-footer-contents {
    align-items: center;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 8px 16px;
}

.tabulator .tabulator-footer .tabulator-page-counter {
    font-weight: 400;
    color: #495057;
}

.tabulator .tabulator-footer .tabulator-paginator {
    color: #212529;
    flex: 1;
    font-family: inherit;
    font-size: inherit;
    font-weight: inherit;
    text-align: right;
}

.tabulator .tabulator-footer .tabulator-page {
    background: rgba(0, 0, 0, 0.05);
    border: 1px solid #ced4da;
    border-radius: 4px;
    display: inline-block;
    margin: 0 2px;
    padding: 4px 8px;
    color: #495057;
    transition: all 0.2s ease;
}

.tabulator .tabulator-footer .tabulator-page.active {
    background: #1976d2;
    color: #ffffff;
    border-color: #1976d2;
}

.tabulator .tabulator-footer .tabulator-page:not(:disabled):hover {
    background: rgba(0, 0, 0, 0.1);
    color: #212529;
    cursor: pointer;
}

/* Placeholder styling */
.tabulator .tabulator-tableholder .tabulator-placeholder .tabulator-placeholder-contents {
    color: #6c757d;
    display: inline-block;
    font-size: 18px;
    font-weight: 500;
    padding: 20px;
    text-align: center;
    white-space: normal;
}

/* Popup and menu styling */
.tabulator-popup-container {
    background: #ffffff;
    border: 1px solid #dee2e6;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    box-sizing: border-box;
    display: inline-block;
    font-size: 14px;
    overflow-y: auto;
    position: absolute;
    z-index: 10000;
    border-radius: 6px;
}

.tabulator-menu .tabulator-menu-item {
    box-sizing: border-box;
    padding: 8px 16px;
    position: relative;
    user-select: none;
    color: #212529;
    transition: background-color 0.15s ease;
}

.tabulator-menu .tabulator-menu-item:not(.tabulator-menu-item-disabled):hover {
    background: #f8f9fa;
    cursor: pointer;
}

/* Edit list styling */
.tabulator-edit-list {
    font-size: 14px;
    max-height: 200px;
    overflow-y: auto;
    background: #ffffff;
    border-radius: 4px;
}

.tabulator-edit-list .tabulator-edit-list-item {
    color: #212529;
    outline: none;
    padding: 8px 12px;
    transition: background-color 0.15s ease;
}

.tabulator-edit-list .tabulator-edit-list-item.active {
    background: #1976d2;
    color: #ffffff;
}

.tabulator-edit-list .tabulator-edit-list-item:hover {
    background: #f8f9fa;
    cursor: pointer;
}

/* Responsive design improvements */
@media (max-width: 768px) {
    .tabulator-row .tabulator-cell {
        padding: 8px 10px;
        font-size: 13px;
    }

    .tabulator .tabulator-header .tabulator-col .tabulator-col-content {
        padding: 6px 10px;
    }
}

/* Focus and accessibility improvements */
.tabulator:focus-within {
    box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.3);
}

.tabulator-row .tabulator-cell:focus {
    outline: 2px solid #1976d2;
    outline-offset: -2px;
}

/* Scrollbar styling for webkit browsers */
.tabulator .tabulator-tableholder::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

.tabulator .tabulator-tableholder::-webkit-scrollbar-track {
    background: #f8f9fa;
}

.tabulator .tabulator-tableholder::-webkit-scrollbar-thumb {
    background: #ced4da;
    border-radius: 4px;
}

.tabulator .tabulator-tableholder::-webkit-scrollbar-thumb:hover {
    background: #adb5bd;
}

/* Additional light theme specific styles */
.tabulator .tabulator-header .tabulator-col .tabulator-header-filter input,
.tabulator .tabulator-header .tabulator-col .tabulator-header-filter select {
    background: #ffffff;
    border: 1px solid #ced4da;
    color: #495057;
    border-radius: 4px;
    padding: 4px 8px;
}

.tabulator .tabulator-header .tabulator-col .tabulator-header-filter input:focus,
.tabulator .tabulator-header .tabulator-col .tabulator-header-filter select:focus {
    border-color: #1976d2;
    box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.2);
}

.tabulator-row .tabulator-cell.tabulator-editing {
    border: 2px solid #1976d2;
    outline: none;
    padding: 8px 10px;
}

.tabulator-row .tabulator-cell.tabulator-editing input,
.tabulator-row .tabulator-cell.tabulator-editing select {
    background: transparent;
    border: none;
    outline: none;
    color: #212529;
}

.tabulator-row .tabulator-cell.tabulator-validation-fail {
    border: 2px solid #dc3545;
}

.tabulator-row .tabulator-cell.tabulator-validation-fail input,
.tabulator-row .tabulator-cell.tabulator-validation-fail select {
    background: transparent;
    border: none;
    color: #dc3545;
}

/* Group styling */
.tabulator-row.tabulator-group {
    background: #e9ecef;
    border-bottom: 1px solid #ced4da;
    border-right: 1px solid #ced4da;
    border-top: 1px solid #ced4da;
    box-sizing: border-box;
    font-weight: 600;
    min-width: 100%;
    padding: 8px 8px 8px 16px;
    color: #495057;
}

.tabulator-row.tabulator-group:hover {
    background-color: #dee2e6;
    cursor: pointer;
}

/* Toggle styling */
.tabulator-toggle {
    background: #e9ecef;
    border: 1px solid #ced4da;
    box-sizing: border-box;
    display: flex;
    flex-direction: row;
    border-radius: 16px;
}

.tabulator-toggle.tabulator-toggle-on {
    background: #1976d2;
}

.tabulator-toggle .tabulator-toggle-switch {
    background: #ffffff;
    border: 1px solid #ced4da;
    box-sizing: border-box;
    border-radius: 50%;
    transition: all 0.2s ease;
}
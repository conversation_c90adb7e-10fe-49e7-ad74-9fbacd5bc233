/* Modern Dark Tabulator Theme with High Contrast - Theme Aware */

/* Main container with modern font */
.tabulator {
    border: 1px solid #2a2a2a;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Aria<PERSON>, sans-serif;
    font-size: 14px;
    overflow: hidden;
    position: relative;
    text-align: left;
    background-color: #0f0f0f;
    color: #e8e8e8;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    transform: translateZ(0);
}

/* Theme-aware background adjustments for dark themes */
[data-theme="dark"] .tabulator,
[data-theme="halloween"] .tabulator,
[data-theme="synthwave"] .tabulator,
[data-theme="forest"] .tabulator,
[data-theme="aqua"] .tabulator,
[data-theme="black"] .tabulator,
[data-theme="luxury"] .tabulator,
[data-theme="dracula"] .tabulator,
[data-theme="business"] .tabulator,
[data-theme="night"] .tabulator,
[data-theme="coffee"] .tabulator,
[data-theme="dim"] .tabulator,
[data-theme="sunset"] .tabulator,
[data-theme="abyss"] .tabulator {
    background-color: #1a1a1a;
    border-color: #404040;
}

/* Theme-aware header styling for dark themes */
[data-theme="dark"] .tabulator .tabulator-header,
[data-theme="halloween"] .tabulator .tabulator-header,
[data-theme="synthwave"] .tabulator .tabulator-header,
[data-theme="forest"] .tabulator .tabulator-header,
[data-theme="aqua"] .tabulator .tabulator-header,
[data-theme="black"] .tabulator .tabulator-header,
[data-theme="luxury"] .tabulator .tabulator-header,
[data-theme="dracula"] .tabulator .tabulator-header,
[data-theme="business"] .tabulator .tabulator-header,
[data-theme="night"] .tabulator .tabulator-header,
[data-theme="coffee"] .tabulator .tabulator-header,
[data-theme="dim"] .tabulator .tabulator-header,
[data-theme="sunset"] .tabulator .tabulator-header,
[data-theme="abyss"] .tabulator .tabulator-header {
    background: linear-gradient(135deg, #ff8c42 0%, #e67e3c 100%);
    color: #000000;
    border-bottom: 2px solid #e67e3c;
}

.tabulator[tabulator-layout=fitDataFill] .tabulator-tableholder .tabulator-table {
    min-width: 100%;
}

.tabulator[tabulator-layout=fitDataTable] {
    display: inline-block;
}

.tabulator.tabulator-block-select,
.tabulator.tabulator-ranges .tabulator-cell:not(.tabulator-editing) {
    user-select: none;
}

/* Header styling with better contrast - fallback for non-theme-aware */
.tabulator .tabulator-header {
    background: linear-gradient(135deg, #ff8c42 0%, #e67e3c 100%);
    border-bottom: 2px solid #e67e3c;
    box-sizing: border-box;
    color: #000000;
    font-weight: 600;
    outline: none;
    overflow: hidden;
    position: relative;
    user-select: none;
    white-space: nowrap;
    width: 100%;
}

.tabulator .tabulator-header.tabulator-header-hidden {
    display: none;
}

.tabulator .tabulator-header .tabulator-header-contents {
    overflow: hidden;
    position: relative;
}

.tabulator .tabulator-header .tabulator-header-contents .tabulator-headers {
    display: inline-block;
}

/* Column headers with improved styling */
.tabulator .tabulator-header .tabulator-col {
    background: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%);
    border-right: 1px solid #404040;
    box-sizing: border-box;
    display: inline-flex;
    flex-direction: column;
    justify-content: flex-start;
    overflow: hidden;
    position: relative;
    text-align: left;
    vertical-align: bottom;
    transition: background-color 0.2s ease;
}

.tabulator .tabulator-header .tabulator-col:hover {
    background: linear-gradient(135deg, #2a2a2a 0%, #3a3a3a 100%);
}

.tabulator .tabulator-header .tabulator-col.tabulator-moving {
    background: #0a0a0a;
    border: 1px solid #555;
    pointer-events: none;
    position: absolute;
}

.tabulator .tabulator-header .tabulator-col.tabulator-range-highlight {
    background-color: #555;
    color: #ffffff;
}

.tabulator .tabulator-header .tabulator-col.tabulator-range-selected {
    background-color: #666;
    color: #ffffff;
}

.tabulator .tabulator-header .tabulator-col .tabulator-col-content {
    box-sizing: border-box;
    padding: 8px 12px;
    position: relative;
}

.tabulator .tabulator-header .tabulator-col .tabulator-col-content .tabulator-header-popup-button {
    padding: 0 8px;
}

.tabulator .tabulator-header .tabulator-col .tabulator-col-content .tabulator-header-popup-button:hover {
    cursor: pointer;
    opacity: 0.7;
}

.tabulator .tabulator-header .tabulator-col .tabulator-col-content .tabulator-col-title-holder {
    position: relative;
}

.tabulator .tabulator-header .tabulator-col .tabulator-col-content .tabulator-col-title {
    box-sizing: border-box;
    overflow: hidden;
    text-overflow: ellipsis;
    vertical-align: bottom;
    white-space: nowrap;
    width: 100%;
    font-weight: 600;
    letter-spacing: 0.5px;
}

.tabulator .tabulator-header .tabulator-col .tabulator-col-content .tabulator-col-title.tabulator-col-title-wrap {
    text-overflow: clip;
    white-space: normal;
}

/* Table body with darker background for better contrast */
.tabulator .tabulator-tableholder {
    overflow: auto;
    position: relative;
    white-space: nowrap;
    width: 100%;
    background-color: #0f0f0f;
}

.tabulator .tabulator-tableholder:focus {
    outline: none;
}

.tabulator .tabulator-tableholder .tabulator-table {
    background-color: #0f0f0f;
    color: #e8e8e8;
    display: inline-block;
    overflow: visible;
    position: relative;
    white-space: nowrap;
}

/* Row styling with high contrast */
.tabulator-row {
    background-color: #1a1a1a;
    box-sizing: border-box;
    min-height: 32px;
    position: relative;
    transition: background-color 0.15s ease;
    border-bottom: 1px solid #2a2a2a;
}

.tabulator-row.tabulator-row-even {
    background-color: #151515;
}

.tabulator-row.tabulator-selectable:hover {
    background-color: #2a2a2a;
    cursor: pointer;
}

.tabulator-row.tabulator-selected {
    background-color: #0066cc;
    color: #ffffff;
}

.tabulator-row.tabulator-selected:hover {
    background-color: #0052a3;
    cursor: pointer;
}

/* Cell styling with better padding and contrast */
.tabulator-row .tabulator-cell {
    border-right: 1px solid #2a2a2a;
    box-sizing: border-box;
    display: inline-block;
    outline: none;
    overflow: hidden;
    padding: 10px 12px;
    position: relative;
    text-overflow: ellipsis;
    vertical-align: middle;
    white-space: nowrap;
    font-weight: 400;
    line-height: 1.4;
    color: #e8e8e8;
}

.tabulator-row .tabulator-cell.tabulator-row-header {
    background: #2a2a2a;
    border-bottom: 1px solid #404040;
    border-right: 1px solid #404040;
    font-weight: 600;
}

/* Footer styling */
.tabulator .tabulator-footer {
    background: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%);
    border-top: 2px solid #404040;
    color: #e8e8e8;
    font-weight: 500;
    user-select: none;
    white-space: nowrap;
}

.tabulator .tabulator-footer .tabulator-footer-contents {
    align-items: center;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 8px 16px;
}

.tabulator .tabulator-footer .tabulator-page-counter {
    font-weight: 400;
    color: #cccccc;
}

.tabulator .tabulator-footer .tabulator-paginator {
    color: #e8e8e8;
    flex: 1;
    font-family: inherit;
    font-size: inherit;
    font-weight: inherit;
    text-align: right;
}

.tabulator .tabulator-footer .tabulator-page {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid #555;
    border-radius: 4px;
    display: inline-block;
    margin: 0 2px;
    padding: 4px 8px;
    color: #e8e8e8;
    transition: all 0.2s ease;
}

.tabulator .tabulator-footer .tabulator-page.active {
    background: #0066cc;
    color: #ffffff;
    border-color: #0066cc;
}

.tabulator .tabulator-footer .tabulator-page:not(:disabled):hover {
    background: rgba(255, 255, 255, 0.2);
    color: #ffffff;
    cursor: pointer;
}

/* Placeholder styling */
.tabulator .tabulator-tableholder .tabulator-placeholder .tabulator-placeholder-contents {
    color: #888888;
    display: inline-block;
    font-size: 18px;
    font-weight: 500;
    padding: 20px;
    text-align: center;
    white-space: normal;
}

/* Popup and menu styling */
.tabulator-popup-container {
    background: #1a1a1a;
    border: 1px solid #404040;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.4);
    box-sizing: border-box;
    display: inline-block;
    font-size: 14px;
    overflow-y: auto;
    position: absolute;
    z-index: 10000;
    border-radius: 6px;
}

.tabulator-menu .tabulator-menu-item {
    box-sizing: border-box;
    padding: 8px 16px;
    position: relative;
    user-select: none;
    color: #e8e8e8;
    transition: background-color 0.15s ease;
}

.tabulator-menu .tabulator-menu-item:not(.tabulator-menu-item-disabled):hover {
    background: #2a2a2a;
    cursor: pointer;
}

/* Edit list styling */
.tabulator-edit-list {
    font-size: 14px;
    max-height: 200px;
    overflow-y: auto;
    background: #1a1a1a;
    border-radius: 4px;
}

.tabulator-edit-list .tabulator-edit-list-item {
    color: #e8e8e8;
    outline: none;
    padding: 8px 12px;
    transition: background-color 0.15s ease;
}

.tabulator-edit-list .tabulator-edit-list-item.active {
    background: #0066cc;
    color: #ffffff;
}

.tabulator-edit-list .tabulator-edit-list-item:hover {
    background: #2a2a2a;
    cursor: pointer;
}

/* Responsive design improvements */
@media (max-width: 768px) {
    .tabulator-row .tabulator-cell {
        padding: 8px 10px;
        font-size: 13px;
    }

    .tabulator .tabulator-header .tabulator-col .tabulator-col-content {
        padding: 6px 10px;
    }
}

/* Focus and accessibility improvements */
.tabulator:focus-within {
    box-shadow: 0 0 0 2px rgba(0, 102, 204, 0.3);
}

.tabulator-row .tabulator-cell:focus {
    outline: 2px solid #0066cc;
    outline-offset: -2px;
}

/* Scrollbar styling for webkit browsers */
.tabulator .tabulator-tableholder::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

.tabulator .tabulator-tableholder::-webkit-scrollbar-track {
    background: #1a1a1a;
}

.tabulator .tabulator-tableholder::-webkit-scrollbar-thumb {
    background: #404040;
    border-radius: 4px;
}

.tabulator .tabulator-tableholder::-webkit-scrollbar-thumb:hover {
    background: #555555;
}

/* Dark mode specific styling for download icons */
.tabulator img[src="/app-icons/arrow-bottom-icon.svg"] {
    filter: invert(1) brightness(1);
}

/* Alternative approach using CSS mask for better control */
.tabulator img[alt="download icon"] {
    filter: invert(1) brightness(1);
}